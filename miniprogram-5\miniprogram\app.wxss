/**app.wxss - 元亨利贞水墨风全局样式**/

/* 🚨🚨🚨 全局强制隐藏左下角黑色圆圈确认按钮 🚨🚨🚨 */
/* 这是最高优先级的全局隐藏规则 */
*[style*="background: black"],
*[style*="background:black"],
*[style*="background-color: black"],
*[style*="background-color:black"],
*[style*="background: #000"],
*[style*="background:#000"],
*[style*="background-color: #000"],
*[style*="background-color:#000"],
*[style*="background: rgb(0, 0, 0)"],
*[style*="background:rgb(0,0,0)"],
*[style*="background-color: rgb(0, 0, 0)"],
*[style*="background-color:rgb(0,0,0)"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -99999 !important;
  width: 0 !important;
  height: 0 !important;
  position: static !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* 特别针对左下角固定定位的黑色元素 */
*[style*="position: fixed"][style*="left: 0"][style*="bottom: 0"],
*[style*="position:fixed"][style*="left:0"][style*="bottom:0"],
*[style*="position: absolute"][style*="left: 0"][style*="bottom: 0"],
*[style*="position:absolute"][style*="left:0"][style*="bottom:0"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 隐藏所有可能的确认按钮 */
.confirm-btn,
.confirm-button,
.floating-confirm,
.fixed-confirm,
.circle-confirm,
.round-confirm,
[class*="confirm"],
[id*="confirm"],
[class*="float"],
[id*="float"] {
  display: none !important;
}

/* 全局变量定义 */
page {
  --ink-black: #1a1a1a;
  --ink-gray: #666666;
  --ink-light: #999999;
  --paper-white: #fafafa;
  --ancient-paper: #f8f8f0;
  --ancient-gold: #d4af37;

  background: var(--paper-white);
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: var(--ink-black);
  line-height: 1.6;
}

/* 容器基础样式 */
.container {
  min-height: 100vh;
  box-sizing: border-box;
  background: var(--paper-white);
}

/* 按钮重置 */
button {
  background: initial;
  border: none;
  outline: none;
  font-family: inherit;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

/* 水墨风基础组件样式 */
.ink-title {
  font-family: 'STSong', '华文宋体', serif;
  font-size: 48rpx;
  font-weight: 500;
  color: var(--ink-black);
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.ink-subtitle {
  font-size: 32rpx;
  color: var(--ink-gray);
  text-align: center;
  margin: 20rpx 0;
}

.ink-text {
  font-size: 28rpx;
  color: var(--ink-black);
  line-height: 1.8;
}

.ink-tip {
  font-size: 24rpx;
  color: var(--ink-light);
  line-height: 1.6;
}

/* 水墨渐变背景 */
.ink-gradient-bg {
  background: linear-gradient(135deg,
    var(--paper-white) 0%,
    var(--ancient-paper) 50%,
    var(--paper-white) 100%);
}

/* 水墨阴影效果 */
.ink-shadow {
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.08);
}

/* 水墨边框 */
.ink-border {
  border: 2rpx solid rgba(26, 26, 26, 0.1);
  border-radius: 16rpx;
}

/* 古典分割线 */
.ink-divider {
  height: 2rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--ink-light) 50%,
    transparent 100%);
  margin: 40rpx 0;
}

/* 水墨动画基础类 */
.ink-fade-in {
  animation: inkFadeIn 0.8s ease-out;
}

@keyframes inkFadeIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 墨迹扩散动画 */
.ink-ripple {
  position: relative;
  overflow: hidden;
}

.ink-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(26, 26, 26, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  opacity: 0;
  pointer-events: none;
}

.ink-ripple:active::before {
  width: 200%;
  height: 200%;
  opacity: 1;
}

/* 响应式布局 */
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 古典文字样式 */
.ancient-text {
  font-family: 'STKaiti', '楷体', serif;
  color: var(--ink-black);
  line-height: 1.8;
}

.ancient-title {
  font-family: 'STSong', '华文宋体', serif;
  font-weight: 500;
  color: var(--ink-black);
}

.ancient-subtitle {
  font-family: 'STKaiti', '楷体', serif;
  color: var(--ink-gray);
  font-size: 24rpx;
}

/* 水墨装饰元素 */
.ink-decoration {
  position: relative;
}

.ink-decoration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(26, 26, 26, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 古典边框样式 */
.ancient-border {
  border: 2rpx solid rgba(212, 175, 55, 0.3);
  border-radius: 16rpx;
  position: relative;
}

.ancient-border::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  border: 1rpx solid rgba(26, 26, 26, 0.1);
  border-radius: 16rpx;
  pointer-events: none;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 20rpx; }
.m-2 { margin: 40rpx; }
.m-3 { margin: 60rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 20rpx; }
.p-2 { padding: 40rpx; }
.p-3 { padding: 60rpx; }
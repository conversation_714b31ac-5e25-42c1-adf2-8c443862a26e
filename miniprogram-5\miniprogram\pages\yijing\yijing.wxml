<!--pages/yijing/yijing.wxml - 周易卦象（六爻）页面模板-->
<view class="yijing-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">周易卦象</view>
    <view class="header-subtitle">朱熹原著 • 传统六爻</view>
  </view>

  <!-- 问事输入 -->
  <view class="question-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <view class="section-title">要问的事</view>
    <ink-input
      label=""
      placeholder="请输入您要占卜的问题"
      value="{{question}}"
      maxlength="100"
      bind:input="onQuestionInput">
    </ink-input>
    <view class="input-tip">请诚心诚意地输入您要问的事情</view>
  </view>

  <!-- 城市选择（真太阳时） -->
  <view class="city-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <view class="section-title">当前地理位置选择</view>
    <view class="city-selection">
      <picker range="{{cityList}}" bindchange="onCityChange" value="{{selectedCityIndex}}">
        <view class="city-picker">
          <text class="city-name">{{selectedCity}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 真太阳时说明 -->
    <view class="solar-time-info" wx:if="{{solarTimeExplanation}}">
      <view class="solar-time-label">真太阳时修正</view>
      <view class="solar-time-text">{{solarTimeExplanation}}</view>
      <view class="solar-time-result" wx:if="{{trueSolarTimeString}}">
        真太阳时：{{trueSolarTimeString}}
      </view>
    </view>
  </view>

  <!-- 起卦方式选择 -->
  <view class="method-selection" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <view class="section-title">选择起卦方式</view>
    <view class="method-grid">
      <view class="method-item {{selectedMethod === 'coins' ? 'active' : ''}}" bindtap="selectMethod" data-method="coins">
        <view class="method-icon">🪙</view>
        <view class="method-name">三枚铜钱</view>
        <view class="method-desc">传统摇卦法</view>
      </view>
      <view class="method-item {{selectedMethod === 'manual' ? 'active' : ''}}" bindtap="selectMethod" data-method="manual">
        <view class="method-icon">👆</view>
        <view class="method-name">手工指定</view>
        <view class="method-desc">直接选择卦象</view>
      </view>
      <view class="method-item {{selectedMethod === 'computer' ? 'active' : ''}}" bindtap="selectMethod" data-method="computer">
        <view class="method-icon">💻</view>
        <view class="method-name">电脑摇卦</view>
        <view class="method-desc">模拟摇卦动画</view>
      </view>
      <view class="method-item {{selectedMethod === 'number' ? 'active' : ''}}" bindtap="selectMethod" data-method="number">
        <view class="method-icon">🔢</view>
        <view class="method-name">数字起卦</view>
        <view class="method-desc">输入数字生成</view>
      </view>
      <view class="method-item {{selectedMethod === 'time' ? 'active' : ''}}" bindtap="selectMethod" data-method="time">
        <view class="method-icon">⏰</view>
        <view class="method-name">时间起卦</view>
        <view class="method-desc">基于当前时间</view>
      </view>
      <view class="method-item {{selectedMethod === 'text' ? 'active' : ''}}" bindtap="selectMethod" data-method="text">
        <view class="method-icon">📝</view>
        <view class="method-name">字数起卦</view>
        <view class="method-desc">文字笔画计算</view>
      </view>
    </view>
  </view>

  <!-- 起卦参数输入 -->
  <view class="params-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing && selectedMethod !== 'coins' && selectedMethod !== 'computer' && selectedMethod !== 'time'}}">
    <!-- 手工指定卦象 -->
    <view wx:if="{{selectedMethod === 'manual'}}" class="manual-section">
      <view class="section-title">选择卦象</view>
      <view class="hexagram-selector">
        <ink-button
          text="{{selectedHexagram ? selectedHexagram.name : '点击选择卦象'}}"
          type="secondary"
          size="medium"
          bind:tap="showHexagramList">
        </ink-button>
      </view>
    </view>

    <!-- 卦象列表弹窗 -->
    <view class="hexagram-list-modal" wx:if="{{showHexagramModal}}">
      <view class="modal-mask" bindtap="hideHexagramList"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">选择卦象</view>
          <view class="modal-close" bindtap="hideHexagramList">✕</view>
        </view>
        <scroll-view class="hexagram-list" scroll-y="true">
          <view class="hexagram-item" wx:for="{{hexagramList}}" wx:key="number" bindtap="selectHexagram" data-index="{{index}}">
            <view class="hexagram-number">{{item.number}}</view>
            <view class="hexagram-name">{{item.name}}</view>
            <view class="hexagram-symbols">{{item.upper}} {{item.lower}}</view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 数字起卦 -->
    <view wx:if="{{selectedMethod === 'number'}}" class="number-section">
      <view class="section-title">输入数字</view>
      <input
        class="number-input"
        type="number"
        placeholder="请输入任意数字"
        value="{{inputNumber}}"
        bindinput="onNumberInput"
      />
    </view>

    <!-- 字数起卦 -->
    <view wx:if="{{selectedMethod === 'text'}}" class="text-section">
      <view class="section-title">输入文字</view>
      <input
        class="text-input"
        placeholder="请输入文字（将根据笔画起卦）"
        value="{{inputText}}"
        bindinput="onTextInput"
      />
    </view>
  </view>

  <!-- 起卦按钮 -->
  <view class="action-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <ink-button
      text="{{getStartButtonText()}}"
      type="primary"
      size="large"
      bind:tap="onStartDivination">
    </ink-button>
  </view>

  <!-- 投币过程 -->
  <view class="throwing-section" wx:if="{{isThrowingCoins}}">
    <view class="throwing-header">
      <view class="throwing-title">手动投币起卦</view>
      <view class="throwing-progress">第{{currentThrow + 1}}爻 / 共6爻</view>
    </view>

    <!-- 手动投币按钮 -->
    <view class="manual-throw-section" wx:if="{{currentThrow < 6}}">
      <ink-button
        text="{{throwButtonText || '投掷第1爻'}}"
        type="primary"
        size="large"
        bind:tap="manualThrowCoins">
      </ink-button>
      <view class="throw-tip">请诚心诚意，点击按钮投掷铜钱</view>
    </view>

    <!-- 已投币结果 -->
    <view class="coin-results" wx:if="{{coinResults.length > 0}}">
      <view class="results-title">投币结果</view>
      <view class="result-list">
        <view class="result-item" wx:for="{{coinResults}}" wx:key="throw">
          <view class="result-header">
            <text class="result-yao">第{{item.throw}}爻</text>
            <text class="result-type">{{item.yaoType}}{{item.isChanging ? ' (动)' : ''}}</text>
          </view>
          <view class="result-coins">
            <text class="coin" wx:for="{{item.coins}}" wx:for-item="coin" wx:key="*this">
              {{coin === 3 ? '正' : '反'}}
            </text>
            <text class="coin-total">= {{item.total}}</text>
          </view>
          <view class="result-symbol">{{item.yaoSymbol}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分析中状态 -->
  <view class="analyzing-section" wx:if="{{isAnalyzing}}">
    <ink-loading type="brush" text="AI智能解析中..."></ink-loading>
  </view>

  <!-- 卦象结果 -->
  <view class="hexagram-section" wx:if="{{hexagram}}">
    <view class="hexagram-header">
      <view class="hexagram-title">卦象结果</view>
      <view class="hexagram-question">所问：{{hexagram.question}}</view>
      <view class="hexagram-time">起卦时间：{{hexagram.time}}</view>
    </view>

    <!-- 完整六爻装卦显示 -->
    <view class="hexagram-display">
      <view class="hexagram-info">
        <view class="hexagram-name">{{hexagram.liuyaoInfo.hexagramName || '卦名未知'}}</view>
        <view class="hexagram-method">起卦方式：{{hexagram.method || '未知'}}</view>
      </view>

      <!-- 完整装卦表 -->
      <view class="liuyao-table">
        <view class="table-header">
          <view class="col-position">爻位</view>
          <view class="col-branch">地支</view>
          <view class="col-relative">六亲</view>
          <view class="col-spirit">六神</view>
          <view class="col-symbol">爻象</view>
          <view class="col-status">状态</view>
        </view>

        <view class="yao-row" wx:for="{{hexagram.yaos}}" wx:key="throw" wx:for-index="index">
          <view class="col-position">{{6-index}}爻</view>
          <view class="col-branch">{{hexagram.liuyaoInfo.branches[5-index] || ''}}</view>
          <view class="col-relative">{{hexagram.liuyaoInfo.relatives[5-index] || ''}}</view>
          <view class="col-spirit">{{hexagram.liuyaoInfo.spirits[5-index] || ''}}</view>
          <view class="col-symbol {{item.isChanging ? 'changing' : ''}}">{{item.yaoSymbol}}</view>
          <view class="col-status">
            <text wx:if="{{item.isChanging}}" class="changing-mark">○</text>
            <text wx:if="{{hexagram.liuyaoInfo.worldResponse && hexagram.liuyaoInfo.worldResponse.world === (6-index)}}" class="world-mark">世</text>
            <text wx:if="{{hexagram.liuyaoInfo.worldResponse && hexagram.liuyaoInfo.worldResponse.response === (6-index)}}" class="response-mark">应</text>
          </view>
        </view>
      </view>

      <!-- 卦象分析信息 -->
      <view class="hexagram-analysis-info">
        <view class="analysis-item">
          <text class="label">世爻：</text>
          <text class="value">第{{hexagram.liuyaoInfo.worldResponse.world || '?'}}爻</text>
        </view>
        <view class="analysis-item">
          <text class="label">应爻：</text>
          <text class="value">第{{hexagram.liuyaoInfo.worldResponse.response || '?'}}爻</text>
        </view>
        <view class="analysis-item" wx:if="{{hexagram.changingYaos && hexagram.changingYaos.length > 0}}">
          <text class="label">动爻：</text>
          <text class="value">第{{hexagram.changingYaos.join('、')}}爻</text>
        </view>
      </view>

      <!-- 变卦和互卦信息 -->
      <view class="derived-hexagrams" wx:if="{{hexagram.changingYaos && hexagram.changingYaos.length > 0}}">
        <view class="section-title">相关卦象</view>
        <view class="derived-item" wx:if="{{hexagram.changedHexagram}}">
          <text class="derived-label">变卦：</text>
          <text class="derived-value">{{hexagram.changedHexagram.name || '计算中...'}}</text>
        </view>
        <view class="derived-item" wx:if="{{hexagram.mutualHexagram}}">
          <text class="derived-label">互卦：</text>
          <text class="derived-value">{{hexagram.mutualHexagram.name || '计算中...'}}</text>
        </view>
        <view class="derived-item" wx:if="{{hexagram.voidBranches && hexagram.voidBranches.length > 0}}">
          <text class="derived-label">空亡：</text>
          <text class="derived-value">{{hexagram.voidBranches.join('、')}}</text>
        </view>
      </view>
    </view>


  </view>

  <!-- 分析结果 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <view class="analysis-header">
      <view class="analysis-title">AI智能解析</view>
    </view>

    <view class="analysis-content">
      <text class="analysis-text">{{analysis}}</text>
    </view>
  </view>

  <!-- 预分析对话面板 -->
  <view id="conversation-container" class="conversation-panel" wx:if="{{showConversationPanel}}">
    <view class="conversation-header">
      <text class="conversation-title">智能预分析</text>
      <text class="conversation-subtitle">为了给您更准确的分析，请回答以下问题</text>
    </view>

    <!-- 对话历史 -->
    <view class="conversation-history">
      <view class="message-item" wx:for="{{conversationHistory}}" wx:key="timestamp">
        <view class="message-role {{item.role}}">
          <text>{{item.role === 'assistant' ? '大师' : '您'}}</text>
        </view>
        <view class="message-content">
          <text>{{item.content}}</text>
        </view>
      </view>
    </view>

    <!-- 当前问题 -->
    <view class="current-question" wx:if="{{currentFollowUp}}">
      <view class="question-text">{{currentFollowUp.text}}</view>
      <view class="question-knowledge" wx:if="{{currentFollowUp.knowledge}}">
        <text class="knowledge-label">💡 知识点：</text>
        <text class="knowledge-text">{{currentFollowUp.knowledge}}</text>
      </view>
    </view>

    <!-- 输入区域 -->
    <view class="conversation-input-area" wx:if="{{isWaitingResponse}}">
      <input
        class="conversation-input"
        placeholder="请输入您的回答..."
        value="{{conversationInput}}"
        bindinput="onConversationInput"
        confirm-type="send"
        bindconfirm="onSendConversationMessage"
      />
      <ink-button
        text="发送"
        type="primary"
        size="small"
        bind:tap="onSendConversationMessage"
        disabled="{{!conversationInput.trim()}}"
        customClass="send-btn-green"
      />
    </view>

    <!-- AI打字中状态 -->
    <view class="typing-indicator" wx:if="{{isTyping}}">
      <text>大师正在思考中...</text>
      <view class="typing-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
    </view>

    <!-- 跳过按钮 -->
    <view class="conversation-actions">
      <ink-button
        text="跳过预分析，直接解卦"
        type="secondary"
        size="small"
        bind:tap="proceedToFinalAnalysis"
      />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-actions" wx:if="{{hexagram && !showConversationPanel}}">
    <ink-button
      text="重新占卜"
      type="secondary"
      size="large"
      bind:tap="onRestart">
    </ink-button>
  </view>

  <!-- 🚨 强制隐藏调试元素的遮罩层 -->
  <view wx:if="{{forceHideDebug}}" class="debug-element-mask"></view>
</view>
// 多轮对话管理系统
// 管理用户对话上下文、历史记录和状态

/**
 * 对话管理器
 * 负责管理用户的对话状态、历史记录和上下文信息
 */
const ConversationManager = {
  // 初始化属性
  conversations: new Map(), // 存储所有对话会话
  maxHistoryLength: 10, // 最大历史记录长度
  sessionTimeout: 30 * 60 * 1000, // 会话超时时间（30分钟）

  /**
   * 创建新的对话会话
   * @param {string} userId - 用户ID
   * @param {string} moduleType - 模块类型（yijing, meihua, bazi, ziwei）
   * @param {object} initialContext - 初始上下文（卦象、八字等信息）
   * @returns {string} 会话ID
   */
  createSession: function(userId, moduleType, initialContext = {}) {
    const sessionId = this.generateSessionId(userId, moduleType);
    
    const session = {
      sessionId: sessionId,
      userId: userId,
      moduleType: moduleType,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      status: 'active', // active, paused, completed
      
      // 对话历史
      messageHistory: [],
      
      // 上下文信息
      context: {
        ...initialContext,
        currentStage: 'initial', // initial, follow_up, deep_analysis
        analysisResults: [],
        userProfile: {},
        preferences: {}
      },
      
      // 分析状态
      analysisState: {
        hasInitialAnalysis: false,
        followUpQuestions: [],
        currentQuestionIndex: 0,
        completedTopics: []
      }
    };
    
    this.conversations.set(sessionId, session);
    console.log(`🆕 创建新对话会话: ${sessionId} (${moduleType})`);

    return sessionId;
  },

  /**
   * 获取对话会话
   * @param {string} sessionId - 会话ID
   * @returns {object|null} 会话对象
   */
  getSession: function(sessionId) {
    const session = this.conversations.get(sessionId);
    
    if (!session) {
      console.warn(`⚠️ 会话不存在: ${sessionId}`);
      return null;
    }
    
    // 检查会话是否超时
    if (this.isSessionExpired(session)) {
      console.log(`⏰ 会话已超时: ${sessionId}`);
      this.endSession(sessionId);
      return null;
    }
    
    // 更新最后活跃时间
    session.lastActiveAt = new Date();
    return session;
  },

  /**
   * 添加消息到对话历史
   * @param {string} sessionId - 会话ID
   * @param {string} role - 角色（user/assistant）
   * @param {string} content - 消息内容
   * @param {object} metadata - 元数据
   */
  addMessage: function(sessionId, role, content, metadata = {}) {
    const session = this.getSession(sessionId);
    if (!session) return false;

    const message = {
      id: this.generateMessageId(),
      role: role, // 'user' | 'assistant'
      content: content,
      timestamp: new Date(),
      metadata: metadata
    };

    session.messageHistory.push(message);
    
    // 限制历史记录长度
    if (session.messageHistory.length > this.maxHistoryLength) {
      session.messageHistory = session.messageHistory.slice(-this.maxHistoryLength);
    }

    console.log(`💬 添加消息到会话 ${sessionId}: ${role} - ${content.substring(0, 50)}...`);
    return true;
  },

  /**
   * 更新会话上下文
   * @param {string} sessionId - 会话ID
   * @param {object} contextUpdate - 上下文更新
   */
  updateContext: function(sessionId, contextUpdate) {
    const session = this.getSession(sessionId);
    if (!session) return false;

    // 深度合并上下文
    session.context = this.deepMerge(session.context, contextUpdate);

    console.log(`🔄 更新会话上下文: ${sessionId}`);
    return true;
  },

  /**
   * 更新会话上下文（别名函数，保持向后兼容）
   * @param {string} sessionId - 会话ID
   * @param {object} contextUpdate - 上下文更新
   */
  updateSessionContext: function(sessionId, contextUpdate) {
    return this.updateContext(sessionId, contextUpdate);
  },

  /**
   * 添加用户回答
   * @param {string} sessionId - 会话ID
   * @param {object} userResponse - 用户回答对象
   */
  addUserResponse: function(sessionId, userResponse) {
    const session = this.getSession(sessionId);
    if (!session) return false;

    // 初始化用户回答数组（如果不存在）
    if (!session.context.userResponses) {
      session.context.userResponses = [];
    }

    // 添加用户回答
    session.context.userResponses.push({
      ...userResponse,
      timestamp: new Date()
    });

    console.log(`💭 添加用户回答到会话 ${sessionId}: ${userResponse.question} -> ${userResponse.answer}`);
    return true;
  },

  /**
   * 设置分析结果
   * @param {string} sessionId - 会话ID
   * @param {object} analysisResult - 分析结果
   */
  setAnalysisResult: function(sessionId, analysisResult) {
    const session = this.getSession(sessionId);
    if (!session) return false;

    session.context.analysisResults.push({
      timestamp: new Date(),
      result: analysisResult
    });

    session.analysisState.hasInitialAnalysis = true;

    console.log(`📊 设置分析结果: ${sessionId}`);
    return true;
  },

  /**
   * 生成智能追问问题
   * @param {string} sessionId - 会话ID
   * @returns {array} 追问问题列表
   */
  generateFollowUpQuestions: function(sessionId) {
    const session = this.getSession(sessionId);
    if (!session) return [];

    const { moduleType, context } = session;
    const lastAnalysis = context.analysisResults[context.analysisResults.length - 1];
    
    if (!lastAnalysis) return [];

    // 根据模块类型和分析结果生成追问问题
    const questions = this.getModuleSpecificQuestions(moduleType, lastAnalysis.result, context);
    
    session.analysisState.followUpQuestions = questions;
    
    console.log(`❓ 生成追问问题: ${sessionId}, 共${questions.length}个问题`);
    return questions;
  },

  /**
   * 获取下一个追问问题
   * @param {string} sessionId - 会话ID
   * @returns {object|null} 下一个问题
   */
  getNextFollowUpQuestion: function(sessionId) {
    const session = this.getSession(sessionId);
    if (!session) return null;

    const { followUpQuestions, currentQuestionIndex } = session.analysisState;
    
    if (currentQuestionIndex >= followUpQuestions.length) {
      return null; // 没有更多问题
    }

    const nextQuestion = followUpQuestions[currentQuestionIndex];
    session.analysisState.currentQuestionIndex++;
    
    return nextQuestion;
  },

  /**
   * 获取对话摘要
   * @param {string} sessionId - 会话ID
   * @returns {object} 对话摘要
   */
  getConversationSummary: function(sessionId) {
    const session = this.getSession(sessionId);
    if (!session) return null;

    return {
      sessionId: sessionId,
      moduleType: session.moduleType,
      duration: new Date() - session.createdAt,
      messageCount: session.messageHistory.length,
      hasAnalysis: session.analysisState.hasInitialAnalysis,
      currentStage: session.context.currentStage,
      completedTopics: session.analysisState.completedTopics
    };
  },

  /**
   * 结束对话会话
   * @param {string} sessionId - 会话ID
   */
  endSession: function(sessionId) {
    const session = this.conversations.get(sessionId);
    if (session) {
      session.status = 'completed';
      session.endedAt = new Date();
      console.log(`🏁 结束对话会话: ${sessionId}`);
    }

    // 可以选择删除或保留会话记录
    // this.conversations.delete(sessionId);
  },

  /**
   * 清理过期会话
   */
  cleanupExpiredSessions: function() {
    let cleanedCount = 0;

    for (const [sessionId, session] of this.conversations.entries()) {
      if (this.isSessionExpired(session)) {
        this.endSession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 清理了${cleanedCount}个过期会话`);
    }
  },

  // ========== 私有方法 ==========

  /**
   * 生成会话ID
   */
  generateSessionId: function(userId, moduleType) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${moduleType}_${userId}_${timestamp}_${random}`;
  },

  /**
   * 生成消息ID
   */
  generateMessageId: function() {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  },

  /**
   * 检查会话是否过期
   */
  isSessionExpired: function(session) {
    return (new Date() - session.lastActiveAt) > this.sessionTimeout;
  },

  /**
   * 深度合并对象
   */
  deepMerge: function(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  },

  /**
   * 根据模块类型获取特定的追问问题
   */
  getModuleSpecificQuestions: function(moduleType, analysisResult, context) {
    const questionTemplates = {
      yijing: [
        { type: 'timing', question: '您希望了解具体的时间节点吗？比如什么时候开始行动最合适？' },
        { type: 'method', question: '您想知道具体应该采取什么行动来改善现状吗？' },
        { type: 'obstacle', question: '您目前遇到的最大困难是什么？我可以从卦象角度为您分析。' },
        { type: 'alternative', question: '如果当前方案不可行，您是否考虑过其他选择？' }
      ],
      meihua: [
        { type: 'environment', question: '您当前的环境和条件如何？这会影响卦象的具体应用。' },
        { type: 'personality', question: '您的性格特点是什么？这有助于我给出更适合的建议。' },
        { type: 'resources', question: '您目前有哪些资源可以利用？' },
        { type: 'timeline', question: '您希望在多长时间内看到结果？' }
      ],
      bazi: [
        { type: 'career', question: '您目前的工作状况如何？是否考虑转换方向？' },
        { type: 'relationship', question: '您的感情状况怎样？有什么具体困扰吗？' },
        { type: 'health', question: '您的身体状况如何？有需要特别注意的地方吗？' },
        { type: 'finance', question: '您的财务状况和理财方式是怎样的？' }
      ],
      ziwei: [
        { type: 'life_goal', question: '您的人生目标和理想是什么？' },
        { type: 'family', question: '您的家庭状况如何？家人关系怎样？' },
        { type: 'social', question: '您的社交圈子和人际关系状况如何？' },
        { type: 'development', question: '您最希望在哪个方面得到发展和提升？' }
      ]
    };

    return questionTemplates[moduleType] || [];
  }
};

// 定期清理过期会话
setInterval(() => {
  ConversationManager.cleanupExpiredSessions();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = {
  ConversationManager,
  conversationManager: ConversationManager
};
